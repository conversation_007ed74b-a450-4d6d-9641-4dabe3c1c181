{"version": 3, "sources": ["../../../../../../../src/server/future/route-modules/app-page/vendored/ssr/entrypoints.ts"], "names": ["React", "ReactDOM", "ReactDOMServerEdge", "ReactJsxDevRuntime", "ReactJsxRuntime", "ReactServerDOMTurbopackClientEdge", "ReactServerDOMWebpackClientEdge", "getAltProxyForBindingsDEV", "type", "pkg", "process", "env", "NODE_ENV", "altType", "altPkg", "replace", "RegExp", "toLowerCase", "Proxy", "get", "_", "prop", "Error", "TURBOPACK", "require"], "mappings": ";;;;;;;;;;;;;;;;;;;;IAqDEA,KAAK;eAALA;;IAGAC,QAAQ;eAARA;;IACAC,kBAAkB;eAAlBA;;IAHAC,kBAAkB;eAAlBA;;IACAC,eAAe;eAAfA;;IAGAC,iCAAiC;eAAjCA;;IACAC,+BAA+B;eAA/BA;;;+DA3DqB;6EACG;uEACU;oEACH;oEAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpC,SAASC,0BACPC,IAA6B,EAC7BC,GAE0C;IAE1C,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAMC,UAAUL,SAAS,cAAc,YAAY;QACnD,MAAMM,SAASL,IAAIM,OAAO,CAAC,IAAIC,OAAOR,MAAM,OAAOK,QAAQI,WAAW;QAEtE,OAAO,IAAIC,MACT,CAAC,GACD;YACEC,KAAIC,CAAC,EAAEC,IAAY;gBACjB,MAAM,IAAIC,MACR,CAAC,gBAAgB,EAAEd,KAAK,WAAW,EAAEC,IAAI,oDAAoD,EAAEY,KAAK,WAAW,EAAER,QAAQ,WAAW,EAAEC,OAAO,yEAAyE,CAAC;YAE3N;QACF;IAEJ;AACF;AAEA,IAAIT,mCAAmCC;AACvC,IAAII,QAAQC,GAAG,CAACY,SAAS,EAAE;IACzB,6DAA6D;IAC7DlB,oCAAoCmB,QAAQ;IAC5C,IAAId,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CN,kCAAkCC,0BAChC,aACA;IAEJ;AACF,OAAO;IACL,6DAA6D;IAC7DD,kCAAkCkB,QAAQ;IAC1C,IAAId,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CP,oCAAoCE,0BAClC,WACA;IAEJ;AACF"}