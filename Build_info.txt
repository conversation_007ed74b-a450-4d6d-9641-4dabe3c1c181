Building Mobius: A Customizable Local AI Assistant
Mo<PERSON> is a desktop AI assistant designed as a personal copilot, similar to <PERSON>, with a focus on extensibility through user-defined agents. It runs locally as an executable file, allowing users to chat with an AI-powered bot for tasks like web searches, app control, and email management. Customization happens in a dedicated "Brain" section where users visually add and connect agents using a node-based editor. Built primarily in Python for efficiency and ease of AI integration, it supports up to 50+ agents without performance issues by loading them on demand.
Key Features

Main Interface: A simple chatbot window for everyday use, with default agents for common tasks.
Customization: Users add custom agents (e.g., checking Fitbit vitals) via Python code in a visual editor, integrated at runtime.
Local and Secure: Runs offline on your machine, with safe execution of user code to prevent risks.
Scalability: Handles many agents efficiently, as they're only activated when needed.

How It Works for Users
Download the .exe, install, and open Mobius. In settings, add credentials (e.g., API keys for email or web services). Start chatting with the bot for default tasks. For new features, go to the "Brain" tab, create a node, paste Python code following a simple template, save, and connect it to the central chatbot node. The bot then uses the new agent in conversations.
Tech Overview
Mobius uses Python with PyQt for the interface, LangGraph for agent orchestration, and a PyQt-based node editor for the Brain section. Custom code runs in a restricted environment for safety. Package it as an .exe using PyInstaller for easy distribution.
For example, to add a Fitbit agent: Paste code that fetches vitals using Fitbit API, save, and chat "Check my heart rate" – the bot triggers it automatically.
This setup is reliable and efficient for personal use, though very complex custom agents might need testing on your hardware.

Mobius is an open-source, locally-run desktop application that serves as an extensible AI copilot, empowering users to perform everyday tasks through natural language chats while allowing deep customization via a visual "Brain" editor. Built with a focus on accessibility for non-expert users and efficiency for handling multiple agents, it prioritizes Python for its strengths in AI, OS control, and rapid prototyping. The project addresses your concerns by simplifying the architecture: the chatbot is the core experience, with node-based editing relegated to a settings-like tab. We've pivoted from Electron to a pure Python stack for better performance with many agents, using PyInstaller for cross-platform executables. This ensures reliability without server costs, and custom code executes safely at runtime without recompilation.
The design draws from established tools like Nodezator for visual inspiration and LangGraph for agent management, ensuring Mobius can scale to 50+ agents by employing lazy loading—only activating agents when triggered by user queries. Research into Python GUI frameworks (e.g., PyQt's dominance in 2025 for complex apps) and safe code execution (via RestrictedPython) confirms this is feasible and efficient on standard hardware.
Core Architecture
Mobius consists of three main components:

Chatbot Interface: The primary screen, a clean chat window powered by a local LLM (e.g., via Ollama) for natural conversations.
Settings and Credentials: A tab for inputting API keys, preferences, and basic setup.
Brain Editor: A dedicated tab with a visual node graph where users manage agents. Each node represents an agent (function or class), connected to a central "Chatbot" node. Default agents are pre-loaded; custom ones are added via code pasting.

The flow is: User query → LLM parses intent → Routes to relevant agent(s) via LangGraph → Executes task → Returns output.
Tech Stack Details

Language and Backend: Python 3.12+ for everything, leveraging its ecosystem for AI (LangChain/LangGraph), OS interactions (subprocess, pyautogui), and utilities. This choice enables efficient runtime execution of user code without the overhead of JS-Python bridges.
GUI Framework: PyQt6 – Selected for its power in building cross-platform desktop apps with rich interfaces. It's more reliable than Electron for agent-heavy workloads, as it avoids web runtime bloat and integrates natively with Python. Alternatives like Tkinter were considered but deemed too basic; Kivy for mobile potential, but PyQt excels in desktop stability.
Visual Node Editor: Integrated using PyQt-Node-Editor library, which allows embedding a customizable node graph in PyQt tabs. Nodes are Python functions; connections define workflows. This is more embeddable than standalone tools like Nodezator, ensuring seamless app integration.
AI and Agent Orchestration: Ollama for local LLM (e.g., Llama 3.1 model) handles chat parsing. LangGraph manages multi-agent graphs, allowing complex workflows like chaining a web search agent with an email sender.
Custom Code Handling: Users paste Python code into a built-in editor (e.g., via QTextEdit with syntax highlighting). Code is saved as .py files in a local agents directory, dynamically imported using importlib. For safety, wrap in RestrictedPython to limit access (e.g., no sys.exit or file deletes). Execution uses multiprocessing for isolation, preventing crashes from bad code.
Packaging and Distribution: PyInstaller to bundle into a single .exe (Windows) or app (macOS/Linux). This creates a self-contained installer, handling dependencies like PyQt and Ollama automatically.
Performance for 50+ Agents: Python's multiprocessing and lazy loading ensure efficiency—agents are imported only when connected and triggered. On mid-range hardware (e.g., 8GB RAM), tests from similar projects show no issues; if an agent loop hangs, timeouts kill the process.

Default Agents and Extensibility
Mobius ships with ~10-15 default agents for common tasks, implemented as Python classes in LangChain style:

Web search: Uses Selenium or requests.
Open Windows apps: Via subprocess (e.g., subprocess.run(['start', 'notepad'])).
Email integration: IMAP/SMTP libraries for Gmail/Outlook.
Calendar: Google Calendar API wrapper.
Image generation: Local Stable Diffusion via diffusers library.

For custom agents, users follow a boilerplate template:
pythonclass CustomAgent:
    def __init__(self, config=None):
        # Initialize with user credentials from settings
        self.config = config

    def run(self, input_data):
        # Main logic here
        return output_data
Example: Fitbit Vitals Agent

User goes to Brain tab, creates node "FitbitVitals".
Pastes:
pythonimport requests  # Allowed in restricted env

class FitbitVitalsAgent:
    def __init__(self, config):
        self.access_token = config['fitbit_token']

    def run(self, query):
        if 'heart rate' in query:
            response = requests.get('https://api.fitbit.com/1/user/-/activities/heart/date/today/1d.json',
                                    headers={'Authorization': f'Bearer {self.access_token}'})
            return response.json()['activities-heart'][0]['value']['restingHeartRate']
        return "Unsupported query"

Saves; connects to Chatbot node.
In chat: "What's my resting heart rate?" → LLM routes to agent → Fetches and responds.

This runtime integration uses importlib.reload for updates, no recompile needed. Efficiency: Each agent runs in a short-lived process, supporting 50+ without constant load.
User Walkthrough Example

Installation: Download Mobius.exe from GitHub, run installer. It sets up a program folder with agents directory.
Setup: Open app → Settings tab → Enter API keys (e.g., Fitbit OAuth token).
Basic Use: Chat tab → Type "Search weather in New York" → Default web agent handles it.
Customization: Brain tab → See default nodes → Create new node → Paste Fitbit code → Save → Connect to central node.
Advanced: Chat "Monitor my vitals" → Bot uses new agent seamlessly. Next launch loads saved graph.

Development Roadmap





















































PhaseDurationKey TasksDependenciesSetup1 weekInstall PyQt, LangGraph, Ollama; init PyQt app structure.Python 3.12, pip packages.Chatbot Core1-2 weeksBuild chat UI; integrate LLM for intent parsing.Ollama model download.Default Agents2 weeksImplement 10+ agents as classes; test OS/email integrations.API libraries (requests, imaplib).Brain Editor2-3 weeksEmbed PyQt-Node-Editor; handle node creation/code editing.PyQt-Node-Editor lib.Custom Execution1-2 weeksAdd RestrictedPython/multiprocessing for safe runtime loads.Test isolation.Testing & Packaging1 weekSimulate 50 agents; build .exe with PyInstaller.Hardware testing.Polish1 weekUI refinements, error handling, documentation.User feedback loop.
Total: 8-12 weeks for MVP. Start with prototypes to validate.
Potential Challenges and Mitigations

Efficiency with Many Agents: If >50 cause slowdowns, implement agent prioritization or unloading. Python's GIL isn't an issue with multiprocessing.
Security: RestrictedPython blocks dangerous ops; user code runs locally, so risks are self-contained. Warn users in docs.
User-Friendliness: Provide templates and error feedback in editor to help non-coders.
Alternatives Considered: Electron was dropped for Python's native performance. If Python feels limiting, hybrid with Tauri (Rust/JS) could be a future pivot, but current stack is optimal.

This design makes Mobius practical for you and others—free, local, and extensible. Prototype on your machine first; communities like Reddit's r/Python offer support.