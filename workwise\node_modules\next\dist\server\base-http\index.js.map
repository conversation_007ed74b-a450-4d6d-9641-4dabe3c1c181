{"version": 3, "sources": ["../../../src/server/base-http/index.ts"], "names": ["BaseNextRequest", "BaseNextResponse", "constructor", "method", "url", "body", "cookies", "_cookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headers", "destination", "redirect", "statusCode", "<PERSON><PERSON><PERSON><PERSON>", "RedirectStatusCode", "PermanentRedirect"], "mappings": ";;;;;;;;;;;;;;;IA0BsBA,eAAe;eAAfA;;IAeAC,gBAAgB;eAAhBA;;;oCAtCa;iCAEH;AAqBzB,MAAeD;IAKpBE,YAAY,AAAOC,MAAc,EAAE,AAAOC,GAAW,EAAE,AAAOC,IAAU,CAAE;aAAvDF,SAAAA;aAAuBC,MAAAA;aAAoBC,OAAAA;IAAa;IAE3E,qDAAqD;IAErD,IAAWC,UAAU;QACnB,IAAI,IAAI,CAACC,QAAQ,EAAE,OAAO,IAAI,CAACA,QAAQ;QACvC,OAAQ,IAAI,CAACA,QAAQ,GAAGC,IAAAA,gCAAe,EAAC,IAAI,CAACC,OAAO;IACtD;AACF;AAEO,MAAeR;IAKpBC,YAAY,AAAOQ,WAAwB,CAAE;aAA1BA,cAAAA;IAA2B;IAmC9C,qDAAqD;IAErDC,SAASD,WAAmB,EAAEE,UAAkB,EAAE;QAChD,IAAI,CAACC,SAAS,CAAC,YAAYH;QAC3B,IAAI,CAACE,UAAU,GAAGA;QAElB,0DAA0D;QAC1D,qCAAqC;QACrC,IAAIA,eAAeE,sCAAkB,CAACC,iBAAiB,EAAE;YACvD,IAAI,CAACF,SAAS,CAAC,WAAW,CAAC,MAAM,EAAEH,YAAY,CAAC;QAClD;QACA,OAAO,IAAI;IACb;AACF"}