{"version": 3, "sources": ["../../../src/server/dev/hot-middleware.ts"], "names": ["WebpackHotMiddleware", "isMiddlewareStats", "stats", "key", "compilation", "entrypoints", "keys", "isMiddlewareFilename", "stats<PERSON><PERSON><PERSON><PERSON>", "to<PERSON><PERSON>", "all", "errors", "hash", "warnings", "getStatsForSyncEvent", "clientStats", "serverStats", "hasErrors", "ts", "EventStream", "constructor", "clients", "Set", "everyClient", "fn", "client", "close", "clear", "handler", "add", "addEventListener", "delete", "publish", "payload", "send", "JSON", "stringify", "compilers", "versionInfo", "onClientInvalid", "closed", "serverLatestStats", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "BUILDING", "onClientDone", "statsResult", "clientLatestStats", "Date", "now", "publishStats", "onServerInvalid", "onServerDone", "onEdgeServerInvalid", "middlewareLatestStats", "onEdgeServerDone", "onHMR", "eventStream", "syncStats", "middlewareStats", "SYNC", "moduleTrace", "BUILT", "hooks", "invalid", "tap", "done"], "mappings": "AAAA,iIAAiI;AACjI,yBAAyB;AAEzB,iDAAiD;AAEjD,wEAAwE;AACxE,kEAAkE;AAClE,sEAAsE;AACtE,sEAAsE;AACtE,qEAAqE;AACrE,wEAAwE;AACxE,4BAA4B;AAE5B,iEAAiE;AACjE,kEAAkE;AAElE,kEAAkE;AAClE,qEAAqE;AACrE,yEAAyE;AACzE,uEAAuE;AACvE,uEAAuE;AACvE,oEAAoE;AACpE,yDAAyD;;;;;+BA+E5CA;;;eAAAA;;;uBA5EwB;kCAGO;AAE5C,SAASC,kBAAkBC,KAAoB;IAC7C,KAAK,MAAMC,OAAOD,MAAME,WAAW,CAACC,WAAW,CAACC,IAAI,GAAI;QACtD,IAAIC,IAAAA,2BAAoB,EAACJ,MAAM;YAC7B,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAASK,YAAYN,KAA4B;IAC/C,IAAI,CAACA,OAAO,OAAO,CAAC;IACpB,OAAOA,MAAMO,MAAM,CAAC;QAClBC,KAAK;QACLC,QAAQ;QACRC,MAAM;QACNC,UAAU;IACZ;AACF;AAEA,SAASC,qBACPC,WAAwD,EACxDC,WAAwD;IAExD,IAAI,CAACD,aAAa,OAAOC,+BAAAA,YAAad,KAAK;IAC3C,IAAI,CAACc,aAAa,OAAOD,+BAAAA,YAAab,KAAK;IAE3C,qDAAqD;IACrD,oGAAoG;IACpG,gEAAgE;IAChE,IAAIc,YAAYd,KAAK,CAACe,SAAS,IAAI;QACjC,OAAOD,YAAYd,KAAK;IAC1B;IAEA,0BAA0B;IAC1B,OAAOc,YAAYE,EAAE,GAAGH,YAAYG,EAAE,GAAGF,YAAYd,KAAK,GAAGa,YAAYb,KAAK;AAChF;AAEA,MAAMiB;IAEJC,aAAc;QACZ,IAAI,CAACC,OAAO,GAAG,IAAIC;IACrB;IAEAC,YAAYC,EAAwB,EAAE;QACpC,KAAK,MAAMC,UAAU,IAAI,CAACJ,OAAO,CAAE;YACjCG,GAAGC;QACL;IACF;IAEAC,QAAQ;QACN,IAAI,CAACH,WAAW,CAAC,CAACE;YAChBA,OAAOC,KAAK;QACd;QACA,IAAI,CAACL,OAAO,CAACM,KAAK;IACpB;IAEAC,QAAQH,MAAU,EAAE;QAClB,IAAI,CAACJ,OAAO,CAACQ,GAAG,CAACJ;QACjBA,OAAOK,gBAAgB,CAAC,SAAS;YAC/B,IAAI,CAACT,OAAO,CAACU,MAAM,CAACN;QACtB;IACF;IAEAO,QAAQC,OAAY,EAAE;QACpB,IAAI,CAACV,WAAW,CAAC,CAACE;YAChBA,OAAOS,IAAI,CAACC,KAAKC,SAAS,CAACH;QAC7B;IACF;AACF;AAEO,MAAMjC;IAQXoB,YAAYiB,SAA6B,EAAEC,WAAwB,CAAE;aAyBrEC,kBAAkB;gBACG;YAAnB,IAAI,IAAI,CAACC,MAAM,MAAI,0BAAA,IAAI,CAACC,iBAAiB,qBAAtB,wBAAwBvC,KAAK,CAACe,SAAS,KAAI;YAC9D,IAAI,CAACe,OAAO,CAAC;gBACXU,QAAQC,6CAA2B,CAACC,QAAQ;YAC9C;QACF;aAEAC,eAAe,CAACC;gBAEK;YADnB,IAAI,CAACC,iBAAiB,GAAG;gBAAE7B,IAAI8B,KAAKC,GAAG;gBAAI/C,OAAO4C;YAAY;YAC9D,IAAI,IAAI,CAACN,MAAM,MAAI,0BAAA,IAAI,CAACC,iBAAiB,qBAAtB,wBAAwBvC,KAAK,CAACe,SAAS,KAAI;YAC9D,IAAI,CAACiC,YAAY,CAACJ;QACpB;aAEAK,kBAAkB;gBACX,yBAED;YAFJ,IAAI,GAAC,0BAAA,IAAI,CAACV,iBAAiB,qBAAtB,wBAAwBvC,KAAK,CAACe,SAAS,KAAI;YAChD,IAAI,CAACwB,iBAAiB,GAAG;YACzB,KAAI,0BAAA,IAAI,CAACM,iBAAiB,qBAAtB,wBAAwB7C,KAAK,EAAE;gBACjC,IAAI,CAACgD,YAAY,CAAC,IAAI,CAACH,iBAAiB,CAAC7C,KAAK;YAChD;QACF;aAEAkD,eAAe,CAACN;YACd,IAAI,IAAI,CAACN,MAAM,EAAE;YACjB,IAAIM,YAAY7B,SAAS,IAAI;gBAC3B,IAAI,CAACwB,iBAAiB,GAAG;oBAAEvB,IAAI8B,KAAKC,GAAG;oBAAI/C,OAAO4C;gBAAY;gBAC9D,IAAI,CAACI,YAAY,CAACJ;YACpB;QACF;aAEAO,sBAAsB;gBACf,6BAED;YAFJ,IAAI,GAAC,8BAAA,IAAI,CAACC,qBAAqB,qBAA1B,4BAA4BpD,KAAK,CAACe,SAAS,KAAI;YACpD,IAAI,CAACqC,qBAAqB,GAAG;YAC7B,KAAI,0BAAA,IAAI,CAACP,iBAAiB,qBAAtB,wBAAwB7C,KAAK,EAAE;gBACjC,IAAI,CAACgD,YAAY,CAAC,IAAI,CAACH,iBAAiB,CAAC7C,KAAK;YAChD;QACF;aAEAqD,mBAAmB,CAACT;YAClB,IAAI,CAAC7C,kBAAkB6C,cAAc;gBACnC,IAAI,CAACK,eAAe;gBACpB,IAAI,CAACC,YAAY,CAACN;gBAClB;YACF;YAEA,IAAIA,YAAY7B,SAAS,IAAI;gBAC3B,IAAI,CAACqC,qBAAqB,GAAG;oBAAEpC,IAAI8B,KAAKC,GAAG;oBAAI/C,OAAO4C;gBAAY;gBAClE,IAAI,CAACI,YAAY,CAACJ;YACpB;QACF;QAEA;;;;;GAKC,QACDU,QAAQ,CAAC/B;YACP,IAAI,IAAI,CAACe,MAAM,EAAE;YACjB,IAAI,CAACiB,WAAW,CAAC7B,OAAO,CAACH;YAEzB,MAAMiC,YAAY5C,qBAChB,IAAI,CAACiC,iBAAiB,EACtB,IAAI,CAACN,iBAAiB;YAGxB,IAAIiB,WAAW;oBAEuB;gBADpC,MAAMxD,QAAQM,YAAYkD;gBAC1B,MAAMC,kBAAkBnD,aAAY,8BAAA,IAAI,CAAC8C,qBAAqB,qBAA1B,4BAA4BpD,KAAK;gBAErE,IAAI,CAAC8B,OAAO,CAAC;oBACXU,QAAQC,6CAA2B,CAACiB,IAAI;oBACxChD,MAAMV,MAAMU,IAAI;oBAChBD,QAAQ;2BAAKT,MAAMS,MAAM,IAAI,EAAE;2BAAOgD,gBAAgBhD,MAAM,IAAI,EAAE;qBAAE;oBACpEE,UAAU;2BACJX,MAAMW,QAAQ,IAAI,EAAE;2BACpB8C,gBAAgB9C,QAAQ,IAAI,EAAE;qBACnC;oBACDyB,aAAa,IAAI,CAACA,WAAW;gBAC/B;YACF;QACF;aAEAY,eAAe,CAACJ;YACd,MAAM5C,QAAQ4C,YAAYrC,MAAM,CAAC;gBAC/BC,KAAK;gBACLE,MAAM;gBACNC,UAAU;gBACVF,QAAQ;gBACRkD,aAAa;YACf;YAEA,IAAI,CAAC7B,OAAO,CAAC;gBACXU,QAAQC,6CAA2B,CAACmB,KAAK;gBACzClD,MAAMV,MAAMU,IAAI;gBAChBC,UAAUX,MAAMW,QAAQ,IAAI,EAAE;gBAC9BF,QAAQT,MAAMS,MAAM,IAAI,EAAE;YAC5B;QACF;aAEAqB,UAAU,CAACC;YACT,IAAI,IAAI,CAACO,MAAM,EAAE;YACjB,IAAI,CAACiB,WAAW,CAACzB,OAAO,CAACC;QAC3B;aACAP,QAAQ;YACN,IAAI,IAAI,CAACc,MAAM,EAAE;YACjB,0EAA0E;YAC1E,sEAAsE;YACtE,IAAI,CAACA,MAAM,GAAG;YACd,IAAI,CAACiB,WAAW,CAAC/B,KAAK;QACxB;QArIE,IAAI,CAAC+B,WAAW,GAAG,IAAItC;QACvB,IAAI,CAAC4B,iBAAiB,GAAG;QACzB,IAAI,CAACO,qBAAqB,GAAG;QAC7B,IAAI,CAACb,iBAAiB,GAAG;QACzB,IAAI,CAACD,MAAM,GAAG;QACd,IAAI,CAACF,WAAW,GAAGA;QAEnBD,SAAS,CAAC,EAAE,CAAC0B,KAAK,CAACC,OAAO,CAACC,GAAG,CAC5B,0BACA,IAAI,CAAC1B,eAAe;QAEtBF,SAAS,CAAC,EAAE,CAAC0B,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC,0BAA0B,IAAI,CAACpB,YAAY;QACvER,SAAS,CAAC,EAAE,CAAC0B,KAAK,CAACC,OAAO,CAACC,GAAG,CAC5B,0BACA,IAAI,CAACd,eAAe;QAEtBd,SAAS,CAAC,EAAE,CAAC0B,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC,0BAA0B,IAAI,CAACb,YAAY;QACvEf,SAAS,CAAC,EAAE,CAAC0B,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC,0BAA0B,IAAI,CAACV,gBAAgB;QAC3ElB,SAAS,CAAC,EAAE,CAAC0B,KAAK,CAACC,OAAO,CAACC,GAAG,CAC5B,0BACA,IAAI,CAACZ,mBAAmB;IAE5B;AAgHF"}