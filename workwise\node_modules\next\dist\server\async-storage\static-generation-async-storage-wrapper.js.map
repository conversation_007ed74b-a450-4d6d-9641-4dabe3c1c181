{"version": 3, "sources": ["../../../src/server/async-storage/static-generation-async-storage-wrapper.ts"], "names": ["StaticGenerationAsyncStorageWrapper", "wrap", "storage", "urlPathname", "renderOpts", "requestEndedState", "callback", "isStaticGeneration", "supportsDynamicResponse", "isDraftMode", "isServerAction", "prerenderState", "experimental", "ppr", "createPrerenderState", "isDebugPPRSkeleton", "store", "pagePath", "originalPathname", "incrementalCache", "globalThis", "__incrementalCache", "isRevalidate", "isPrerendering", "nextExport", "fetchCache", "isOnDemandRevalidate", "run"], "mappings": ";;;;+BAgDaA;;;eAAAA;;;kCA1CwB;AA0C9B,MAAMA,sCAGT;IACFC,MACEC,OAAiD,EACjD,EAAEC,WAAW,EAAEC,UAAU,EAAEC,iBAAiB,EAA2B,EACvEC,QAAkD;QAElD;;;;;;;;;;;;;;;;KAgBC,GACD,MAAMC,qBACJ,CAACH,WAAWI,uBAAuB,IACnC,CAACJ,WAAWK,WAAW,IACvB,CAACL,WAAWM,cAAc;QAE5B,MAAMC,iBACJJ,sBAAsBH,WAAWQ,YAAY,CAACC,GAAG,GAC7CC,IAAAA,sCAAoB,EAACV,WAAWW,kBAAkB,IAClD;QAEN,MAAMC,QAA+B;YACnCT;YACAJ;YACAc,UAAUb,WAAWc,gBAAgB;YACrCC,kBACE,qEAAqE;YACrE,mDAAmD;YACnDf,WAAWe,gBAAgB,IAAI,AAACC,WAAmBC,kBAAkB;YACvEC,cAAclB,WAAWkB,YAAY;YACrCC,gBAAgBnB,WAAWoB,UAAU;YACrCC,YAAYrB,WAAWqB,UAAU;YACjCC,sBAAsBtB,WAAWsB,oBAAoB;YAErDjB,aAAaL,WAAWK,WAAW;YAEnCE;YACAN;QACF;QAEA,sFAAsF;QACtFD,WAAWY,KAAK,GAAGA;QAEnB,OAAOd,QAAQyB,GAAG,CAACX,OAAOV,UAAUU;IACtC;AACF"}